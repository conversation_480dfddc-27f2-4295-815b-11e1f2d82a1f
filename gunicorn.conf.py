"""
Gunicorn配置文件
用于生产环境部署
"""
import os
import multiprocessing

# 服务器配置
bind = "0.0.0.0:8000"
workers = min(4, multiprocessing.cpu_count())  # 限制最大worker数量
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 50

# 超时配置
timeout = 120
keepalive = 2
graceful_timeout = 30

# 日志配置
loglevel = "info"
accesslog = "-"  # 输出到stdout
errorlog = "-"   # 输出到stderr
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
preload_app = True  # 预加载应用，确保配置在所有worker中一致
daemon = False
pidfile = "/tmp/gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

def on_starting(server):
    """服务器启动时的回调"""
    server.log.info("Gunicorn服务器正在启动...")

def on_reload(server):
    """重新加载时的回调"""
    server.log.info("Gunicorn服务器正在重新加载...")

def when_ready(server):
    """服务器准备就绪时的回调"""
    server.log.info("Gunicorn服务器已准备就绪")

def worker_int(worker):
    """Worker进程中断时的回调"""
    worker.log.info("Worker进程收到中断信号")

def pre_fork(server, worker):
    """Fork worker进程前的回调"""
    server.log.info(f"正在启动worker进程 {worker.pid}")

def post_fork(server, worker):
    """Fork worker进程后的回调"""
    server.log.info(f"Worker进程 {worker.pid} 已启动")

def post_worker_init(worker):
    """Worker进程初始化后的回调"""
    worker.log.info(f"Worker进程 {worker.pid} 初始化完成")

def worker_abort(worker):
    """Worker进程异常终止时的回调"""
    worker.log.info(f"Worker进程 {worker.pid} 异常终止")

def pre_exec(server):
    """执行前的回调"""
    server.log.info("服务器准备执行")

def pre_request(worker, req):
    """处理请求前的回调"""
    worker.log.debug(f"开始处理请求: {req.uri}")

def post_request(worker, req, environ, resp):
    """处理请求后的回调"""
    worker.log.debug(f"请求处理完成: {req.uri} - {resp.status}")
