#!/usr/bin/env python3
"""
测试API配置加载的脚本
用于验证在不同导入顺序下配置是否正确加载
"""

def test_direct_import():
    """测试直接导入api_utils的情况"""
    print("=== 测试1: 直接导入api_utils ===")
    from utils.api_utils import get_api_base_url, get_api_md5_key
    
    print(f"API_BASE_URL: {get_api_base_url()}")
    print(f"API_MD5_KEY: {get_api_md5_key()}")

def test_after_config_load():
    """测试在配置加载后导入的情况"""
    print("\n=== 测试2: 先加载配置再导入 ===")
    from config.app_config import load_config
    load_config()
    
    from utils.api_utils import get_api_base_url, get_api_md5_key
    
    print(f"API_BASE_URL: {get_api_base_url()}")
    print(f"API_MD5_KEY: {get_api_md5_key()}")

def test_tool_import():
    """测试通过工具模块导入的情况"""
    print("\n=== 测试3: 通过工具模块导入 ===")
    from tools.hardware_tools import get_managed_bars
    from utils.api_utils import get_api_base_url, get_api_md5_key
    
    print(f"API_BASE_URL: {get_api_base_url()}")
    print(f"API_MD5_KEY: {get_api_md5_key()}")

if __name__ == "__main__":
    test_direct_import()
    test_after_config_load()
    test_tool_import()
