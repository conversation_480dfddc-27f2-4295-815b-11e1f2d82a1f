"""
API应用模块
FastAPI应用和API路由定义
"""
import os
from config.app_config import load_config

# 加载配置（优先使用properties文件，否则使用环境变量）
load_config()

from fastapi import FastAPI, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from config.app_config import check_required_env_vars, get_mysql_config, get_heartbeat_interval, get_redis_config
from core.db.mysql_pool import AsyncMySQLPool
from core.db.redis_service import RedisService
from utils.log_utils import get_logger
from api.middleware.error_handlers import (
    global_exception_handler,
    validation_exception_handler,
    http_exception_handler
)

import sys
import asyncio

# 日志系统已经在app_config模块中配置好了
logger = get_logger(__name__)

# 全局变量存储心跳任务
heartbeat_task = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    global heartbeat_task
    
    # 检查必要的环境变量
    is_valid, error_msg = check_required_env_vars()
    if not is_valid:
        logger.critical(error_msg)
        sys.exit(1)
    
    # 启动时初始化连接池
    try:
        # 初始化MySQL连接池
        AsyncMySQLPool.initialize(**get_mysql_config())
        
        # 初始化连接池，确保应用启动时就创建连接
        await AsyncMySQLPool.get_pool()
        logger.debug("数据库连接池初始化成功")
        
        # 启动数据库连接心跳任务
        heartbeat_interval = get_heartbeat_interval()
        logger.debug(f"启动数据库连接心跳，间隔 {heartbeat_interval} 秒")
        heartbeat_task = asyncio.create_task(AsyncMySQLPool.keep_alive(interval=heartbeat_interval))
        
        # 初始化Redis服务
        redis_config = get_redis_config()
        await RedisService.initialize(**redis_config)
        logger.debug("Redis服务初始化成功")
        
        # 订阅停止事件频道
        from api.services.chat_service import ChatService
        await RedisService.subscribe("stop_generation", ChatService.handle_stop_message)
        logger.info("已订阅停止事件频道")
        
    except ValueError as e:
        logger.critical(f"数据库连接池初始化失败，参数错误: {e}")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"应用初始化失败: {e}")
        sys.exit(1)
    
    yield
    
    # 应用关闭时取消心跳任务
    if heartbeat_task and not heartbeat_task.done():
        logger.info("正在停止数据库心跳任务...")
        heartbeat_task.cancel()
        try:
            await heartbeat_task
        except asyncio.CancelledError:
            logger.info("数据库心跳任务已停止")
    
    # 应用关闭时关闭Redis连接
    try:
        await RedisService.close()
        logger.info("Redis服务已关闭")
    except Exception as e:
        logger.error(f"关闭Redis服务时出错: {e}")
    
    # 应用关闭时关闭连接池
    try:
        await AsyncMySQLPool.close()
        logger.info("数据库连接池已关闭")
    except ConnectionResetError as e:
        logger.warning(f"关闭连接池时捕获到连接重置错误: {e}")
    except Exception as e:
        logger.error(f"关闭连接池时出错: {e}")

app = FastAPI(lifespan=lifespan)

# 添加CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有请求头
)

# 从控制器导入路由
from api.controllers.chat_controller import router as chat_router
# 添加路由
app.include_router(chat_router)

# 注册错误处理器
app.add_exception_handler(Exception, global_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler) 