"""
LLM模型配置模块
负责创建和配置语言模型实例
"""
from langchain_openai import ChatOpenAI
from langchain.chat_models import init_chat_model
from langchain_deepseek import ChatDeepSeek
from langchain_community.chat_models.tongyi import ChatTongyi
import os

model_name = os.getenv("AGENT_MODEL_NAME", "deepseek-v3")
if "deepseek" in model_name.lower():
    llm = init_chat_model(model=os.getenv("AGENT_MODEL_NAME", "deepseek-chat"), tags=["wangwei"], model_provider="deepseek", temperature=0.2)
else:
    llm = init_chat_model(model=os.getenv("AGENT_MODEL_NAME", "qwen-turbo-latest"), tags=["wangwei"], model_provider="openai", temperature=0.2)

intent_model_name = os.getenv("INTENT_MODEL_NAME", "deepseek-chat")
if "deepseek" in intent_model_name.lower():
    intent_llm = init_chat_model(model=os.getenv("INTENT_MODEL_NAME", "deepseek-chat"), tags=["intent"], model_provider="deepseek", temperature=0)
else:
    intent_llm = init_chat_model(model=os.getenv("INTENT_MODEL_NAME", "qwen-turbo-latest"), tags=["intent"], model_provider="openai", temperature=0)

title_model_name = os.getenv("TITLE_MODEL_NAME", "qwen3-1.7b")
if "deepseek" in title_model_name.lower():
    title_llm = init_chat_model(model=os.getenv("TITLE_MODEL_NAME", "deepseek-chat"), tags=["title"], model_provider="deepseek", temperature=0.2)
else:
    title_llm = init_chat_model(model=os.getenv("TITLE_MODEL_NAME", "qwen3-1.7b"), tags=["title"], model_provider="openai", temperature=0.2)