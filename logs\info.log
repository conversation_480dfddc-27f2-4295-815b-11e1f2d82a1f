2025-08-25 11:15:12,653 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:15:12,655 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:15:12,656 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:15:12,657 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:15:12,686 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:15:12,687 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:15:18,082 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:15:18,083 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:15:18,083 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:15:18,084 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:15:18,134 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:15:18,134 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:33:41,937 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:33:41,939 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:33:41,940 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:33:41,941 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:33:42,002 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:33:42,003 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:46:51,923 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:46:51,924 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:46:51,925 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:46:51,926 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:46:51,995 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:46:51,996 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:49:35,866 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:49:35,867 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:49:35,868 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:49:35,869 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:49:35,892 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:49:35,893 - api.app - INFO - 已订阅停止事件频道
2025-08-25 14:34:51,459 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 14:34:51,460 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 14:34:51,460 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 14:34:51,462 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 14:34:51,498 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 14:34:51,499 - api.app - INFO - 已订阅停止事件频道
2025-08-25 14:39:39,585 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 14:39:39,586 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 14:39:39,587 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 14:39:39,588 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 14:39:39,639 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 14:39:39,639 - api.app - INFO - 已订阅停止事件频道
2025-08-25 16:54:57,325 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 16:54:57,328 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 16:54:57,328 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 16:54:57,329 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 16:54:57,345 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 16:54:57,346 - api.app - INFO - 已订阅停止事件频道
2025-08-25 17:56:15,694 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 17:56:15,695 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 17:56:15,696 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 17:56:15,697 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 17:56:15,750 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 17:56:15,751 - api.app - INFO - 已订阅停止事件频道
2025-08-25 18:02:23,668 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 18:02:23,670 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 18:02:23,670 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 18:02:23,671 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 18:02:23,735 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 18:02:23,736 - api.app - INFO - 已订阅停止事件频道
2025-08-25 18:10:52,759 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 18:10:52,761 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 18:10:52,762 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 18:10:52,764 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 18:10:52,801 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 18:10:52,802 - api.app - INFO - 已订阅停止事件频道
2025-08-25 18:16:06,164 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 18:16:06,165 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 18:16:06,166 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 18:16:06,167 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 18:16:06,184 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 18:16:06,185 - api.app - INFO - 已订阅停止事件频道
2025-08-25 18:16:17,349 - api.app - INFO - 正在停止数据库心跳任务...
2025-08-25 18:16:17,350 - api.app - INFO - 数据库心跳任务已停止
2025-08-25 18:16:17,351 - core.db.redis_service - INFO - Redis PubSub已关闭
2025-08-25 18:16:17,352 - core.db.redis_service - INFO - Redis连接已关闭
2025-08-25 18:16:17,352 - api.app - INFO - Redis服务已关闭
2025-08-25 18:16:17,353 - api.app - INFO - 数据库连接池已关闭
2025-08-25 18:19:06,299 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 18:19:06,301 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 18:19:06,301 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 18:19:06,303 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 18:19:06,337 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 18:19:06,338 - api.app - INFO - 已订阅停止事件频道
2025-08-25 18:21:36,528 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 18:21:36,529 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 18:21:36,530 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 18:21:36,532 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 18:21:36,548 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 18:21:36,548 - api.app - INFO - 已订阅停止事件频道
